import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:vocadex/src/features/auth/providers/auth_state_provider.dart';
import 'package:vocadex/src/services/firebase_service.dart';

part 'user_notification_preferences_provider.g.dart';

@riverpod
class UserNotificationPreferencesNotifier extends _$UserNotificationPreferencesNotifier {
  @override
  Future<Map<String, bool>> build() async {
    final authState = ref.watch(authStateProvider);
    
    return authState.when(
      authenticated: (user) async {
        try {
          final firestore = FirebaseFirestore.instance;
          final doc = await firestore.collection('users').doc(user.uid).get();
          
          if (doc.exists) {
            final data = doc.data();
            final prefs = data?['notificationPreferences'] as Map<String, dynamic>?;
            
            if (prefs != null) {
              return Map<String, bool>.from(prefs);
            }
          }
          
          // Return default preferences if not found
          return {
            'pushNotifications': true,
            'emailNotifications': true,
          };
        } catch (e) {
          debugPrint('Error fetching notification preferences: $e');
          return {
            'pushNotifications': true,
            'emailNotifications': true,
          };
        }
      },
      unauthenticated: () async => {
        'pushNotifications': true,
        'emailNotifications': true,
      },
      loading: () async => {
        'pushNotifications': true,
        'emailNotifications': true,
      },
      error: (_) async => {
        'pushNotifications': true,
        'emailNotifications': true,
      },
    );
  }

  /// Update push notification preference
  Future<void> updatePushNotifications(bool enabled) async {
    final authState = ref.read(authStateProvider);
    
    await authState.whenOrNull(
      authenticated: (user) async {
        try {
          final currentPrefs = state.valueOrNull ?? {};
          final updatedPrefs = {
            ...currentPrefs,
            'pushNotifications': enabled,
          };
          
          // Update in Firestore
          final firestore = FirebaseFirestore.instance;
          await firestore.collection('users').doc(user.uid).update({
            'notificationPreferences': updatedPrefs,
          });
          
          // Update local state
          state = AsyncData(updatedPrefs);
          
          debugPrint('Push notifications ${enabled ? 'enabled' : 'disabled'}');
        } catch (e) {
          debugPrint('Error updating push notification preference: $e');
          rethrow;
        }
      },
    );
  }

  /// Update email notification preference
  Future<void> updateEmailNotifications(bool enabled) async {
    final authState = ref.read(authStateProvider);
    
    await authState.whenOrNull(
      authenticated: (user) async {
        try {
          final currentPrefs = state.valueOrNull ?? {};
          final updatedPrefs = {
            ...currentPrefs,
            'emailNotifications': enabled,
          };
          
          // Update in Firestore
          final firestore = FirebaseFirestore.instance;
          await firestore.collection('users').doc(user.uid).update({
            'notificationPreferences': updatedPrefs,
          });
          
          // Update local state
          state = AsyncData(updatedPrefs);
          
          debugPrint('Email notifications ${enabled ? 'enabled' : 'disabled'}');
        } catch (e) {
          debugPrint('Error updating email notification preference: $e');
          rethrow;
        }
      },
    );
  }

  /// Refresh preferences from Firestore
  Future<void> refresh() async {
    state = const AsyncLoading();
    state = await AsyncValue.guard(() => build());
  }

  /// Get specific preference value
  bool getPreference(String key) {
    final prefs = state.valueOrNull ?? {};
    return prefs[key] ?? true;
  }

  /// Update multiple preferences at once
  Future<void> updatePreferences(Map<String, bool> preferences) async {
    final authState = ref.read(authStateProvider);
    
    await authState.whenOrNull(
      authenticated: (user) async {
        try {
          final currentPrefs = state.valueOrNull ?? {};
          final updatedPrefs = {
            ...currentPrefs,
            ...preferences,
          };
          
          // Update in Firestore
          final firestore = FirebaseFirestore.instance;
          await firestore.collection('users').doc(user.uid).update({
            'notificationPreferences': updatedPrefs,
          });
          
          // Update local state
          state = AsyncData(updatedPrefs);
          
          debugPrint('Notification preferences updated: $preferences');
        } catch (e) {
          debugPrint('Error updating notification preferences: $e');
          rethrow;
        }
      },
    );
  }
}

/// Provider for getting push notification preference
@riverpod
bool pushNotificationEnabled(Ref ref) {
  final prefsAsync = ref.watch(userNotificationPreferencesNotifierProvider);
  return prefsAsync.valueOrNull?['pushNotifications'] ?? true;
}

/// Provider for getting email notification preference
@riverpod
bool emailNotificationEnabled(Ref ref) {
  final prefsAsync = ref.watch(userNotificationPreferencesNotifierProvider);
  return prefsAsync.valueOrNull?['emailNotifications'] ?? true;
}
