// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_notification_preferences_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pushNotificationEnabledHash() =>
    r'2ac88b487e879a22610d1760344dcc43f46d5a14';

/// Provider for getting push notification preference
///
/// Copied from [pushNotificationEnabled].
@ProviderFor(pushNotificationEnabled)
final pushNotificationEnabledProvider = AutoDisposeProvider<bool>.internal(
  pushNotificationEnabled,
  name: r'pushNotificationEnabledProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$pushNotificationEnabledHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PushNotificationEnabledRef = AutoDisposeProviderRef<bool>;
String _$emailNotificationEnabledHash() =>
    r'cfa35dc5ac4ae40381c2e33a956a9e6f5cfa563b';

/// Provider for getting email notification preference
///
/// Copied from [emailNotificationEnabled].
@ProviderFor(emailNotificationEnabled)
final emailNotificationEnabledProvider = AutoDisposeProvider<bool>.internal(
  emailNotificationEnabled,
  name: r'emailNotificationEnabledProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$emailNotificationEnabledHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef EmailNotificationEnabledRef = AutoDisposeProviderRef<bool>;
String _$userNotificationPreferencesNotifierHash() =>
    r'c9dcf9825cc8af12d4eb2c8e1ac43019c753a374';

/// See also [UserNotificationPreferencesNotifier].
@ProviderFor(UserNotificationPreferencesNotifier)
final userNotificationPreferencesNotifierProvider =
    AutoDisposeAsyncNotifierProvider<UserNotificationPreferencesNotifier,
        Map<String, bool>>.internal(
  UserNotificationPreferencesNotifier.new,
  name: r'userNotificationPreferencesNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userNotificationPreferencesNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserNotificationPreferencesNotifier
    = AutoDisposeAsyncNotifier<Map<String, bool>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
